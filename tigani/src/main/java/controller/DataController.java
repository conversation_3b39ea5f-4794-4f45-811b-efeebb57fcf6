package controller;

import com.mongodb.client.model.Filters;
import core.Core;
import dao.BaseDao;
import dao.DaoFilters;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.*;
import spark.Request;
import spark.Response;
import spark.Route;
import utils.RequestUtils;

import java.util.*;

/**
 * DataController provides AJAX endpoints for populating select fields
 * with remote data from Country, City, Province entities.
 * Supports search functionality and pagination for Preline UI Advanced Select.
 *
 * <AUTHOR>
 */
public class DataController {

    private static final Logger LOGGER = LoggerFactory.getLogger(DataController.class.getName());

    /**
     * Endpoint for Country data with search and pagination support.
     * Returns JSON format compatible with Preline UI Advanced Select.
     *
     * Parameters:
     * - q: search query (searches in description field)
     * - page: page number (default: 1)
     * - limit: items per page (default: 20)
     * - selected: preselected value that must be included in first page results
     */
    public static Route be_data_countries = (Request request, Response response) -> {
        response.type("application/json");

        try {
            // Parse request parameters
            Map<String, String> params = new LinkedHashMap<>();
            RequestUtils.parseRequest(request, params, null);

            String searchQuery = params.getOrDefault("q", "");
            String selected = params.getOrDefault("selected", "");
            int page = Integer.parseInt(params.getOrDefault("page", "1"));
            int limit = Integer.parseInt(params.getOrDefault("limit", "20"));
            int skip = (page - 1) * limit;

            LOGGER.debug("Country data request - query: '{}', selected: '{}', page: {}, limit: {}",
                searchQuery, selected, page, limit);

            List<Country> countries = new ArrayList<>();
            Set<String> addedCodes = new HashSet<>(); // Track added items to avoid duplicates

            // Strategy: For first page with selected value, ensure selected is included
            if (page == 1 && StringUtils.isNotBlank(selected) && StringUtils.isBlank(searchQuery)) {
                // First, try to get the selected country by code (primary key)
                List<Bson> selectedFilters = Arrays.asList(Filters.eq("code", selected));
                QueryOptions selectedQuery = DaoFilters.createQueryWithOptions(
                    selectedFilters, 0, 1, "description", "asc");
                List<Country> selectedCountries = BaseDao.getDocumentsByFilters(Country.class, selectedQuery, null, false, false);

                // If not found by code, try by description
                if (selectedCountries.isEmpty()) {
                    selectedFilters = Arrays.asList(Filters.eq("description", selected));
                    selectedQuery = DaoFilters.createQueryWithOptions(
                        selectedFilters, 0, 1, "description", "asc");
                    selectedCountries = BaseDao.getDocumentsByFilters(Country.class, selectedQuery, null, false, false);
                }

                // Add selected country first
                for (Country country : selectedCountries) {
                    countries.add(country);
                    addedCodes.add(country.getCode());
                }
            }

            // Calculate remaining slots for search results
            int remainingLimit = limit - countries.size();

            if (remainingLimit > 0) {
                // Build search filters
                List<Bson> searchFilters = new ArrayList<>();

                if (StringUtils.isNotBlank(searchQuery)) {
                    searchFilters.add(Filters.regex("description", "(?i).*" + searchQuery.trim() + ".*"));
                }

                // Exclude already added countries to avoid duplicates
                if (!addedCodes.isEmpty()) {
                    searchFilters.add(Filters.nin("code", addedCodes));
                }

                // Create query for remaining results
                QueryOptions searchQueryOptions = DaoFilters.createQueryWithOptions(
                    searchFilters, skip, remainingLimit, "description", "asc");

                List<Country> searchResults = BaseDao.getDocumentsByFilters(Country.class, searchQueryOptions, null, false, false);
                countries.addAll(searchResults);
            }

            // Build response in Preline UI format
            Map<String, Object> jsonResponse = new HashMap<>();
            List<Map<String, Object>> results = new ArrayList<>();

            for (Country country : countries) {
                Map<String, Object> item = new HashMap<>();
                item.put("id", country.getCode());  // Use code as ID
                item.put("text", country.getDescription());  // Use description as display text
                results.add(item);
            }

            jsonResponse.put("results", results);

            // Add pagination info for infinite scroll support
            boolean hasMore = countries.size() == limit;  // If we got full page, there might be more
            jsonResponse.put("pagination", Map.of("more", hasMore));

            LOGGER.debug("Returning {} countries for query '{}' (selected: '{}')", results.size(), searchQuery, selected);

            return Core.serializeToJson(jsonResponse);

        } catch (Exception e) {
            LOGGER.error("Error retrieving country data", e);
            response.status(500);
            return Core.serializeToJson(Map.of("error", "Internal server error"));
        }
    };

    /**
     * Endpoint for City data with search and pagination support.
     * Returns JSON format compatible with Preline UI Advanced Select.
     *
     * Parameters:
     * - q: search query (searches in name field)
     * - page: page number (default: 1)
     * - limit: items per page (default: 20)
     * - selected: preselected value that must be included in first page results
     * - provinceCode: optional filter by province code
     * - countryCode: optional filter by country code
     */
    public static Route be_data_cities = (Request request, Response response) -> {
        response.type("application/json");

        try {
            // Parse request parameters
            Map<String, String> params = new LinkedHashMap<>();
            RequestUtils.parseRequest(request, params, null);

            String searchQuery = params.getOrDefault("q", "");
            String selected = params.getOrDefault("selected", "");
            int page = Integer.parseInt(params.getOrDefault("page", "1"));
            int limit = Integer.parseInt(params.getOrDefault("limit", "20"));
            int skip = (page - 1) * limit;
            String provinceCode = params.get("provinceCode");
            String countryCode = params.get("countryCode");

            LOGGER.debug("City data request - query: '{}', selected: '{}', page: {}, limit: {}, provinceCode: '{}', countryCode: '{}'",
                    searchQuery, selected, page, limit, provinceCode, countryCode);

            List<City> cities = new ArrayList<>();
            Set<String> addedNames = new HashSet<>(); // Track added items to avoid duplicates

            // Strategy: For first page with selected value, ensure selected is included
            if (page == 1 && StringUtils.isNotBlank(selected) && StringUtils.isBlank(searchQuery)) {
                // Build filters for selected city
                List<Bson> selectedFilters = new ArrayList<>();
                selectedFilters.add(Filters.eq("name", selected));

                // Apply province/country filters if specified
                if (StringUtils.isNotBlank(provinceCode)) {
                    selectedFilters.add(Filters.eq("provinceCode", provinceCode));
                }
                if (StringUtils.isNotBlank(countryCode)) {
                    selectedFilters.add(Filters.eq("countryCode", countryCode));
                }

                QueryOptions selectedQuery = DaoFilters.createQueryWithOptions(
                    selectedFilters, 0, 1, "name", "asc");
                List<City> selectedCities = BaseDao.getDocumentsByFilters(City.class, selectedQuery, null, false, false);

                // Add selected city first
                for (City city : selectedCities) {
                    cities.add(city);
                    addedNames.add(city.getName());
                }
            }

            // Calculate remaining slots for search results
            int remainingLimit = limit - cities.size();

            if (remainingLimit > 0) {
                // Build search filters
                List<Bson> searchFilters = new ArrayList<>();

                if (StringUtils.isNotBlank(searchQuery)) {
                    searchFilters.add(Filters.regex("name", "(?i).*" + searchQuery.trim() + ".*"));
                }

                // Apply province/country filters
                if (StringUtils.isNotBlank(provinceCode)) {
                    searchFilters.add(Filters.eq("provinceCode", provinceCode));
                }
                if (StringUtils.isNotBlank(countryCode)) {
                    searchFilters.add(Filters.eq("countryCode", countryCode));
                }

                // Exclude already added cities to avoid duplicates
                if (!addedNames.isEmpty()) {
                    searchFilters.add(Filters.nin("name", addedNames));
                }

                // Create query for remaining results
                QueryOptions searchQueryOptions = DaoFilters.createQueryWithOptions(
                    searchFilters, skip, remainingLimit, "name", "asc");

                List<City> searchResults = BaseDao.getDocumentsByFilters(City.class, searchQueryOptions, null, false, false);
                cities.addAll(searchResults);
            }

            // Build response in Preline UI format
            Map<String, Object> jsonResponse = new HashMap<>();
            List<Map<String, Object>> results = new ArrayList<>();

            for (City city : cities) {
                Map<String, Object> item = new HashMap<>();
                item.put("id", city.getName());  // Use name as ID
                // Display format: "CityName (Province)"
                String displayText = city.getName();
                if (StringUtils.isNotBlank(city.getProvince())) {
                    displayText += " (" + city.getProvince() + ")";
                }
                item.put("text", displayText);
                results.add(item);
            }

            jsonResponse.put("results", results);

            // Add pagination info for infinite scroll support
            boolean hasMore = cities.size() == limit;  // If we got full page, there might be more
            jsonResponse.put("pagination", Map.of("more", hasMore));

            LOGGER.debug("Returning {} cities for query '{}' (selected: '{}')", results.size(), searchQuery, selected);

            return Core.serializeToJson(jsonResponse);

        } catch (Exception e) {
            LOGGER.error("Error retrieving city data", e);
            response.status(500);
            return Core.serializeToJson(Map.of("error", "Internal server error"));
        }
    };

    /**
     * Endpoint for Province data with search and pagination support.
     * Returns JSON format compatible with Preline UI Advanced Select.
     *
     * Parameters:
     * - q: search query (searches in description field)
     * - page: page number (default: 1)
     * - limit: items per page (default: 20)
     * - selected: preselected value that must be included in first page results
     */
    public static Route be_data_provinces = (Request request, Response response) -> {
        response.type("application/json");

        try {
            // Parse request parameters
            Map<String, String> params = new LinkedHashMap<>();
            RequestUtils.parseRequest(request, params, null);

            String searchQuery = params.getOrDefault("q", "");
            String selected = params.getOrDefault("selected", "");
            int page = Integer.parseInt(params.getOrDefault("page", "1"));
            int limit = Integer.parseInt(params.getOrDefault("limit", "20"));
            int skip = (page - 1) * limit;

            LOGGER.debug("Province data request - query: '{}', selected: '{}', page: {}, limit: {}",
                searchQuery, selected, page, limit);

            List<Province> provinces = new ArrayList<>();
            Set<String> addedCodes = new HashSet<>(); // Track added items to avoid duplicates

            // Strategy: For first page with selected value, ensure selected is included
            if (page == 1 && StringUtils.isNotBlank(selected) && StringUtils.isBlank(searchQuery)) {
                // First, try to get the selected province by code (primary key)
                List<Bson> selectedFilters = Arrays.asList(Filters.eq("code", selected));
                QueryOptions selectedQuery = DaoFilters.createQueryWithOptions(
                    selectedFilters, 0, 1, "description", "asc");
                List<Province> selectedProvinces = BaseDao.getDocumentsByFilters(Province.class, selectedQuery, null, false, false);

                // If not found by code, try by description
                if (selectedProvinces.isEmpty()) {
                    selectedFilters = Arrays.asList(Filters.eq("description", selected));
                    selectedQuery = DaoFilters.createQueryWithOptions(
                        selectedFilters, 0, 1, "description", "asc");
                    selectedProvinces = BaseDao.getDocumentsByFilters(Province.class, selectedQuery, null, false, false);
                }

                // Add selected province first
                for (Province province : selectedProvinces) {
                    provinces.add(province);
                    addedCodes.add(province.getCode());
                }
            }

            // Calculate remaining slots for search results
            int remainingLimit = limit - provinces.size();

            if (remainingLimit > 0) {
                // Build search filters
                List<Bson> searchFilters = new ArrayList<>();

                if (StringUtils.isNotBlank(searchQuery)) {
                    searchFilters.add(Filters.regex("description", "(?i).*" + searchQuery.trim() + ".*"));
                }

                // Exclude already added provinces to avoid duplicates
                if (!addedCodes.isEmpty()) {
                    searchFilters.add(Filters.nin("code", addedCodes));
                }

                // Create query for remaining results
                QueryOptions searchQueryOptions = DaoFilters.createQueryWithOptions(
                    searchFilters, skip, remainingLimit, "description", "asc");

                List<Province> searchResults = BaseDao.getDocumentsByFilters(Province.class, searchQueryOptions, null, false, false);
                provinces.addAll(searchResults);
            }

            // Build response in Preline UI format
            Map<String, Object> jsonResponse = new HashMap<>();
            List<Map<String, Object>> results = new ArrayList<>();

            for (Province province : provinces) {
                Map<String, Object> item = new HashMap<>();
                item.put("id", province.getCode());  // Use code as ID
                item.put("text", province.getDescription());  // Use description as display text
                results.add(item);
            }

            jsonResponse.put("results", results);

            // Add pagination info for infinite scroll support
            boolean hasMore = provinces.size() == limit;  // If we got full page, there might be more
            jsonResponse.put("pagination", Map.of("more", hasMore));

            LOGGER.debug("Returning {} provinces for query '{}' (selected: '{}')", results.size(), searchQuery, selected);

            return Core.serializeToJson(jsonResponse);

        } catch (Exception e) {
            LOGGER.error("Error retrieving province data", e);
            response.status(500);
            return Core.serializeToJson(Map.of("error", "Internal server error"));
        }
    };

    /**
     * Endpoint for retrieving a single entity (city, province, or country) by type and value.
     * Returns the entity only if exactly one match is found.
     *
     * Parameters:
     * - entityType: type of entity to search ("city", "province", "country")
     * - value: search value
     *
     * Search behavior:
     * - Province/Country: searches by "code" field (exact match)
     * - City: searches by "name" field (case-insensitive regex) with optional provinceCode/countryCode filters
     */
    public static Route be_data_entity = (Request request, Response response) -> {
        response.type("application/json");

        try {
            // Parse request parameters
            Map<String, String> params = new LinkedHashMap<>();
            RequestUtils.parseRequest(request, params, null);

            String entityType = params.get("entityType");
            String value = params.get("value");

            LOGGER.debug("Entity data request - entityType: '{}', value: '{}'", entityType, value);

            // Validate required parameters
            if (StringUtils.isBlank(entityType)) {
                response.status(400);
                return Core.serializeToJson(Map.of("error", "entityType parameter is required"));
            }

            if (StringUtils.isBlank(value)) {
                response.status(400);
                return Core.serializeToJson(Map.of("error", "value parameter is required"));
            }

            // Switch case for different entity types
            Object result = null;
            switch (entityType.toLowerCase()) {
                case "country":
                    result = searchCountryByCode(value);
                    break;
                case "province":
                    result = searchProvinceByCode(value);
                    break;
                case "city":
                    String provinceCode = params.get("provinceCode");
                    String countryCode = params.get("countryCode");
                    result = searchCityByName(value, provinceCode, countryCode);
                    break;
                default:
                    response.status(400);
                    return Core.serializeToJson(Map.of("error", "Invalid entityType. Allowed values: city, province, country"));
            }

            if (result == null) {
                response.status(404);
                return Core.serializeToJson(Map.of("error", "No entity found"));
            }

            LOGGER.debug("Found entity for entityType '{}' and value '{}'", entityType, value);
            return Core.serializeToJson(result);

        } catch (Exception e) {
            LOGGER.error("Error retrieving entity data", e);
            response.status(500);
            return Core.serializeToJson(Map.of("error", "Internal server error"));
        }
    };

    /**
     * Search for a country by code (exact match).
     * Returns the country if exactly one match is found, null otherwise.
     */
    private static Country searchCountryByCode(String code) throws Exception {
        List<Bson> filters = List.of(Filters.eq("code", code));
        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 2, null, null);

        List<Country> countries = BaseDao.getDocumentsByFilters(Country.class, queryOptions, null, false, false);

        // Return only if exactly one result found
        return (countries != null && countries.size() == 1) ? countries.get(0) : null;
    }

    /**
     * Search for a province by code (exact match).
     * Returns the province if exactly one match is found, null otherwise.
     */
    private static Province searchProvinceByCode(String code) throws Exception {
        List<Bson> filters = List.of(Filters.eq("code", code));
        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 2, null, null);

        List<Province> provinces = BaseDao.getDocumentsByFilters(Province.class, queryOptions, null, false, false);

        // Return only if exactly one result found
        return (provinces != null && provinces.size() == 1) ? provinces.get(0) : null;
    }

    /**
     * Search for a city by name (case-insensitive regex) with optional province and country filters.
     * Returns the city if exactly one match is found, null otherwise.
     */
    private static City searchCityByName(String name, String provinceCode, String countryCode) throws Exception {
        List<Bson> filters = new ArrayList<>();

        // Case-insensitive regex search on name field - using partial match as requested
        filters.add(Filters.regex("name", "(?i).*" + name.trim() + ".*"));

        // Add optional province filter
        if (StringUtils.isNotBlank(provinceCode)) {
            filters.add(Filters.eq("provinceCode", provinceCode));
        }

        // Add optional country filter
        if (StringUtils.isNotBlank(countryCode)) {
            filters.add(Filters.eq("countryCode", countryCode));
        }

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 2, null, null);

        List<City> cities = BaseDao.getDocumentsByFilters(City.class, queryOptions, null, false, false);

        // Return only if exactly one result found
        return (cities != null && cities.size() == 1) ? cities.get(0) : null;
    }
}
